"use client";

import React, { useState, useEffect } from 'react';
import { useRouter } from 'next/navigation';
import { Search, MapPin, Calendar as CalendarIcon, Users, Sparkles, CheckCircle2, XCircle, Star } from 'lucide-react';
import { Input } from '@/components/ui/input';
import { Button } from '@/components/ui/button';
import { Card, CardContent } from '@/components/ui/card';
import { Popover, PopoverContent, PopoverTrigger } from '@/components/ui/popover';
import { Calendar } from '@/components/ui/calendar';
import { format, startOfToday } from 'date-fns';
import { cn } from '@/lib/utils';
import { useQuery } from 'react-query';
import { getInterest } from '@/app/actions/get-interest';
import { useMediaQuery } from 'react-responsive';
import { useDispatch } from 'react-redux';
import { changeDate, changeDestination, changeDestinationId } from '@/app/store/features/searchPackageSlice';
import { selectAdultsChild } from '@/app/store/features/roomCapacitySlice';
import { selectTheme, selectThemeId } from '@/app/store/features/selectThemeSlice';
import { NEXT_PUBLIC_IMAGE_URL } from '@/app/utils/constants/apiUrls';

// Define interfaces
interface Destination {
  destinationId: string;
  destinationName: string;
  popular?: boolean;
  isDomestic: boolean;
}

interface Interest {
  image: string;
  interestId: string;
  interestName: string;
  isFirst: boolean;
  sort: number;
  _id: string;
}

const SearchPackage = () => {
  const FEATURED_DESTINATIONS = [
    { name: 'Goa', tag: 'POPULAR', color: 'bg-purple-100 text-purple-800' },
    { name: 'Kashmir', tag: 'HONEYMOON', color: 'bg-pink-100 text-pink-800' },
    { name: 'Manali', tag:'HONEYMOON', color: 'bg-green-100 text-green-800' },
    { name: 'Ooty', tag: 'BUDGET', color: 'bg-orange-100 text-orange-800'},
    { name: 'Munnar', tag: 'TRENDING', color: 'bg-blue-100 text-blue-800' },
    { name: 'Andaman', tag: 'IN SEASON', color: 'bg-green-100 text-green-800'},
    { name: 'Kodaikanal', tag: 'IN SEASON', color: 'bg-orange-100 text-orange-800'},
    { name: 'Coorg', tag: 'BUDGET', color: 'bg-blue-100 text-blue-800'},
    { name: 'Alleppey', tag: 'BACKWATERS', color: 'bg-green-100 text-green-800' },
    { name: 'Kochi', tag: 'BUDGET', color: 'bg-blue-100 text-blue-800'},
    { name: 'Shimla', tag: 'BUDGET', color: 'bg-orange-100 text-orange-800'},
    { name: 'Bali', tag: 'HONEYMOON', color: 'bg-green-100 text-green-800'},
    { name: 'Maldives', tag: 'HONEYMOON', color: 'bg-pink-100 text-pink-800'},
  ];
  const router = useRouter();
  const dispatch = useDispatch();
  const isMobile = useMediaQuery({ query: '(max-width: 768px)' });

  // State management
  const [activeField, setActiveField] = useState<string | null>(null);
  const [location, setLocation] = useState('Manali');
  const [selectedDestinationId, setSelectedDestinationId] = useState('');
  const [date, setDate] = useState<Date | undefined>(startOfToday());
  const [adults, setAdults] = useState(2);
  const [children, setChildren] = useState(0);
  const [rooms, setRooms] = useState(1);
  const [selectedTheme, setSelectedTheme] = useState('Couple');
  const [selectedThemeId, setSelectedThemeId] = useState('');
  const [focused, setFocused] = useState('Couple');

  // Room calculation states (from search-progress)
  const [minRooms, setMinRooms] = useState(1);
  const [showChild, setShowChild] = useState(true);

  // Destination search states
  const [allDestinations, setAllDestinations] = useState<Destination[]>([]);
  const [showDestinationPicker, setShowDestinationPicker] = useState(false);
  const [isLoading, setIsLoading] = useState(false);
  const [searchQuery, setSearchQuery] = useState('Manali');

  // Fetch themes
  const { data: themes, isLoading: themesLoading } = useQuery<Interest[]>("fetch Interest", getInterest);

  const navigate = (path: string) => {
    router.push(path);
  };

  

  // Room calculation logic from search-progress
  useEffect(() => {
    let r = 0;
    if (children == 0) {
      if (focused !== "Honeymoon") r = Math.ceil(adults / 3);
      else r = Math.ceil(adults / 2);
    } else {
      let x = adults;
      let y = children;
      r = 0;
      while (x >= 3 && y >= 1) {
        x = x - 3;
        y = y - 1;
        r++;
      }
      while (x > 0 || y > 0) {
        x = x - 3;
        y = y - 3;
        r++;
      }
    }
    setRooms(r);
    setMinRooms(r);
  }, [children, adults, focused]);

  useEffect(() => {
    switch (focused) {
      case "Couple": {
        setShowChild(true);
        break;
      }
      case "Honeymoon": {
        setAdults(2)
        setChildren(0);
        setShowChild(false);
        break;
      }
      default: {
        setShowChild(true);
      }
    }
  }, [focused]);

  // Ensure default selected theme id when themes load (for Couple)
  useEffect(() => {
    if (!selectedThemeId && themes && selectedTheme) {
      const found = themes.find(t => t.interestName.toLowerCase() === selectedTheme.toLowerCase());
      if (found) setSelectedThemeId(found.interestId);
    }
  }, [themes, selectedTheme, selectedThemeId]);

  // Fetch destinations from API
  useEffect(() => {
    const fetchDestinations = async () => {
      try {
        setIsLoading(true);
        let url = 'https://api.tripxplo.com/v1/api/user/package/destination/search';

        if (searchQuery.trim() !== '') {
          url += `?search=${encodeURIComponent(searchQuery)}`;
        }

        const response = await fetch(url);
        const data = await response.json();
        if (data.result) {
          const destinations = data.result.map((dest: any) => ({
            ...dest,
            popular: isPopularDestination(dest.destinationName),
            isDomestic: dest.destinationType === 'Domestic',
          }));
          setAllDestinations(destinations);

          // Auto-select Manali destinationId by default if available
          if (!selectedDestinationId && location) {
            const match = destinations.find((d: Destination) => d.destinationName.toLowerCase() === location.toLowerCase());
            if (match) setSelectedDestinationId(match.destinationId);
          }
        }
      } catch (error) {
        console.error('Error fetching destinations:', error);
      } finally {
        setIsLoading(false);
      }
    };

    fetchDestinations();
  }, [searchQuery]);

  // Helper function to determine popular destinations
  const isPopularDestination = (name: string) => {
    const popularNames = ['Bali', 'Goa', 'Manali', 'Varkala', 'Kashmir', 'Kerala'];
    return popularNames.some(popular => name.toLowerCase().includes(popular.toLowerCase()));
  };

  const handleThemeFocus = (theme: Interest) => {
    setFocused(theme.interestName);
    setSelectedTheme(theme.interestName);
    setSelectedThemeId(theme.interestId);

    if (theme.interestName === 'Honeymoon' || theme.interestName === 'Couple') {
      setAdults(2);
      setChildren(0);
    } else if (theme.interestName === 'Family') {
      setAdults(2);
      setChildren(2);
    } else if (theme.interestName === 'Friends') {
      setAdults(4);
      setChildren(0);
    }
  };

  const handleAdultsChange = (newAdults: number) => {
    if (focused === "Honeymoon" && newAdults < 2) {
        return;
    }
    setAdults(newAdults);
  };

  const handleChildrenChange = (newChildren: number) => {
    if (!showChild) {
        setChildren(0);
        return;
    }
    setChildren(newChildren);
  };

  const getGuestText = () => {
    const total = adults + children;
    if (total === 0) return "Add guests";
    if (total === 1) return "1 guest";
    return `${total} guests`;
  };

  const handleDestinationSelect = (destination: string, destinationId: string) => {
    setLocation(destination);
    setSelectedDestinationId(destinationId);
    setActiveField("date");
  };

  const handleSearch = () => {
    // Prepare final data
    const finalData = {
      destination: location,
      destinationId: selectedDestinationId,
      date: date,
      rooms: rooms,
      adults: adults,
      children: children,
      theme: selectedTheme,
      themeId: selectedThemeId,
    };

    // Dispatch to Redux store
    dispatch(changeDestination(finalData.destination));
    dispatch(changeDestinationId(finalData.destinationId));
    if (finalData.date) {
      dispatch(changeDate(finalData.date.toISOString()));
    }

    // Dispatch theme to Redux store
    dispatch(selectTheme({ selectedTheme: finalData.theme }));
    dispatch(selectThemeId({ selectedThemeId: finalData.themeId }));

    // Dispatch room and traveler data to Redux store
    dispatch(selectAdultsChild({
      room: {
        adult: finalData.adults,
        child: finalData.children,
        room: finalData.rooms,
      },
    }));

    // Navigate to packages page
    router.push("/packages");
  };

  return (
    <section id="hero-section" className="relative w-full h-[500px] sm:h-[600px] min-h-[500px] sm:min-h-[600px] flex flex-col items-center justify-center">
      {/* Background with a more vibrant and inviting travel image */}
      <div
        className="absolute inset-0 bg-cover bg-center bg-no-repeat h-full"
        style={{
          backgroundImage: "url('https://images.unsplash.com/photo-1512100356356-de1b84283e18?ixlib=rb-4.0.3&auto=format&fit=crop&w=2070&q=80')"
        }}
      >
        <div className="absolute inset-0 bg-gradient-to-b from-black/60 via-black/40 to-black/70 h-full"></div>
      </div>

      {/* Content */}
      <div className="relative z-10 text-center px-4 max-w-5xl mx-auto">
        <h1 className="text-4xl md:text-7xl font-semibold text-white mb-4 leading-tight animate-fade-in-up">
          Seamless Travel, <span className="text-emerald-400 font-extrabold italic">Unforgettable</span> Experiences
        </h1>

        {/* Enhanced Airbnb-style Search Bar */}
        <div className="w-full max-w-4xl mx-auto mb-12 mt-8 search-bar-container">
          <div className="bg-white rounded-full shadow-lg border border-gray-200 p-1.5 flex items-center relative overflow-visible h-16">
            {/* Animated background indicator */}
            <div
              className={`absolute top-1.5 bottom-1.5 bg-white rounded-full shadow-md transition-all duration-300 ease-in-out ${
                activeField === "where" ? "left-1.5 right-[66.66%]" :
                activeField === "date" ? "left-[33.33%] right-[33.33%]" :
                activeField === "who" ? "left-[66.66%] right-1.5" :
                "left-1.5 right-[66.66%] opacity-0"
              }`}
            />

            {/* Where */}
            <Popover open={activeField === "where"} onOpenChange={(isOpen) => setActiveField(isOpen ? "where" : null)}>
              <PopoverTrigger asChild>
                <div
                  className={`flex-1 px-4 py-3 rounded-full cursor-pointer transition-all duration-300 relative z-10 ${
                    activeField === "where" ? "bg-transparent" : ""
                  }`}
                >
                  <div className="flex flex-col">
                    <label className="text-xs font-semibold text-gray-900 mb-1">Where</label>
                    <Input
                      value={location}
                      onChange={(e) => {
                        setLocation(e.target.value);
                        setSearchQuery(e.target.value);
                      }}
                      placeholder="Search destinations"
                      className="border-0 p-0 text-sm text-gray-600 placeholder:text-gray-400 focus-visible:ring-0 bg-transparent"
                      onFocus={() => setActiveField("where")}
                    />
                  </div>
                </div>
              </PopoverTrigger>
              <PopoverContent className="w-96 bg-white rounded-2xl shadow-2xl mt-2 z-[120] border max-h-96 overflow-hidden p-0">
                <div className="p-4 max-h-96 overflow-y-auto">
                  {/* Search Results from API */}
                  {isLoading ? (
                    <div className="space-y-2">
                      {[...Array(6)].map((_, index) => (
                        <div key={index} className="animate-pulse h-12 bg-slate-200 rounded-lg"></div>
                      ))}
                    </div>
                  ) : allDestinations.length > 0 ? (
                    <>
                      {/* Domestic Section */}
                      {allDestinations.filter(dest => dest.isDomestic).length > 0 && (
                        <div className="mb-4">
                          <h4 className="text-sm font-bold text-blue-600 mb-2 px-3 py-2 bg-blue-50 rounded-lg">DOMESTIC DESTINATIONS</h4>
                          <div className="space-y-1">
                            {allDestinations.filter(dest => dest.isDomestic).map((dest, index) => {
                              const featured = FEATURED_DESTINATIONS.find(f => f.name.toLowerCase() === dest.destinationName.toLowerCase());
                              return (
                                <div
                                  key={`domestic-${index}`}
                                  onClick={() => {
                                    handleDestinationSelect(dest.destinationName, dest.destinationId);
                                  }}
                                  className="flex items-center justify-between p-3 hover:bg-gray-50 cursor-pointer transition-all duration-200 rounded-lg group hover:shadow-sm"
                                >
                                  <span className="text-gray-800 font-medium group-hover:text-gray-900">
                                    {dest.destinationName}
                                  </span>
                                  {featured && (
                                    <span className={`text-xs font-bold px-2 py-1 rounded-full ${featured.color}`}>
                                      {featured.tag}
                                    </span>
                                  )}
                                </div>
                              );
                            })}
                          </div>
                        </div>
                      )}

                      {/* International Section */}
                      {allDestinations.filter(dest => !dest.isDomestic).length > 0 && (
                        <div className="border-t pt-4">
                          <h4 className="text-sm font-bold text-green-600 mb-2 px-3 py-2 bg-green-50 rounded-lg">INTERNATIONAL DESTINATIONS</h4>
                          <div className="space-y-1">
                            {allDestinations.filter(dest => !dest.isDomestic).map((dest, index) => {
                              const featured = FEATURED_DESTINATIONS.find(f => f.name.toLowerCase() === dest.destinationName.toLowerCase());
                              return (
                                <div
                                  key={`international-${index}`}
                                  onClick={() => {
                                    handleDestinationSelect(dest.destinationName, dest.destinationId);
                                  }}
                                  className="flex items-center justify-between p-3 hover:bg-gray-50 cursor-pointer transition-all duration-200 rounded-lg group hover:shadow-sm"
                                >
                                  <span className="text-gray-800 font-medium group-hover:text-gray-900">
                                    {dest.destinationName}
                                  </span>
                                  {featured && (
                                    <span className={`text-xs font-bold px-2 py-1 rounded-full ${featured.color}`}>
                                      {featured.tag}
                                    </span>
                                  )}
                                </div>
                              );
                            })}
                          </div>
                        </div>
                      )}
                    </>
                  ) : (
                    <div className="text-center py-8">
                      <MapPin className="mx-auto h-8 w-8 text-gray-400 mb-2" />
                      <p className="text-sm text-gray-500">No destinations found</p>
                    </div>
                  )}
                </div>
              </PopoverContent>
            </Popover>

            {/* Divider */}
            <div className="w-px h-8 bg-gray-300"></div>

            {/* Date */}
            <Popover open={activeField === "date"}>
              <PopoverTrigger asChild>
                <div
                  className={`flex-1 px-4 py-3 rounded-full cursor-pointer transition-all duration-300 relative z-10 ${
                    activeField === "date" ? "bg-transparent" : "hover:bg-gray-50"
                  }`}
                  onClick={() => {
                    setActiveField(activeField === "date" ? null : "date");
                  }}
                >
                  <div className="flex flex-col">
                    <label className="text-xs font-semibold text-gray-900 mb-1">Date</label>
                    <span className="text-sm text-gray-600">{date ? format(date, "MMM d") : "Add date"}</span>
                  </div>
                </div>
              </PopoverTrigger>
              <PopoverContent className="w-auto p-0 z-[100]" align="center">
                <div className="bg-white/90 backdrop-blur-sm rounded-3xl shadow-2xl border-2 border-gray-100 p-6 hover:shadow-3xl transition-all duration-300">
                  <Calendar
                    mode="single"
                    selected={date}
                    onSelect={(newDate) => {
                      setDate(newDate ?? startOfToday());
                      if (newDate) {
                        setActiveField("who");
                      }
                    }}
                    disabled={(d) => d < startOfToday()}
                    initialFocus
                    className="w-full"
                    classNames={{
                      table: "w-full border-spacing-1",
                      head_cell: "w-full md:w-[50px] font-semibold text-sm md:text-base text-gray-700 pb-2 text-center",
                      cell: "w-full h-10 sm:w-[51px] md:h-[50px] lg:w-[50px] md:h-[50px] p-0.5",
                      row: "flex w-full justify-stretch gap-6",
                      day: "rounded-xl w-full h-full text-sm md:text-base font-medium hover:bg-blue-50 hover:text-blue-600 transition-all duration-200 hover:scale-105 hover:shadow-sm flex items-center justify-center",
                      day_selected: "bg-gradient-to-br from-[#ff7865] to-[#f95d47] text-white rounded-xl shadow-lg hover:shadow-xl transform scale-105",
                      day_today: "bg-gradient-to-br from-[#23cd92] to-[#1fb584] text-white rounded-xl shadow-md",
                      day_disabled: "opacity-40 cursor-not-allowed hover:bg-transparent hover:text-gray-400 hover:scale-100",
                      caption_label: "text-lg md:text-xl font-bold text-gray-800 mb-2",
                      nav_button: "rounded-full w-8 h-8 md:w-10 md:h-10 hover:bg-gray-100 transition-all duration-200 hover:scale-110 border border-gray-200 shadow-sm",
                      nav_button_previous: " hover:bg-blue-50 hover:border-blue-200",
                      nav_button_next: "hover:bg-blue-50 hover:border-blue-200",
                      caption: "flex justify-center items-center mb-4 relative",
                    }}
                  />
                </div>
              </PopoverContent>
            </Popover>

            {/* Divider */}
            <div className="w-px h-8 bg-gray-300"></div>

            {/* Who */}
            <Popover open={activeField === "who"}>
              <PopoverTrigger asChild>
                <div
                  className={`flex-1 px-4 py-3 rounded-full cursor-pointer transition-all duration-300 relative z-10 ${
                    activeField === "who" ? "bg-transparent" : "hover:bg-gray-50"
                  }`}
                  onClick={() => {
                    setActiveField(activeField === "who" ? null : "who");
                  }}
                >
                  <div className="flex flex-col">
                    <label className="text-xs font-semibold text-gray-900 mb-1">Who</label>
                    <span className="text-sm text-gray-600">{getGuestText()}</span>
                  </div>
                </div>
              </PopoverTrigger>
              <PopoverContent className="w-96 p-6 z-[100]" align="end" sideOffset={8}>
                <div className="space-y-6">
                  {/* Themes Selection */}
                  <div>
                    <h4 className="font-semibold text-gray-900 mb-3 flex items-center gap-2">
                      <Sparkles className="w-4 h-4 text-orange-500" />
                      Trip Theme
                    </h4>
                    {themesLoading ? (
                      <div className="grid grid-cols-3 gap-2">
                        {[...Array(6)].map((_, index) => (
                          <div key={index} className="w-full animate-pulse h-16 bg-slate-200 rounded-lg"></div>
                        ))}
                      </div>
                    ) : (
                      <div className="grid grid-cols-3 gap-2 mb-4">
                        {themes?.slice(0, 6).map((theme) => {
                          const isSelected = selectedTheme === theme.interestName;
                          return (
                            <Card
                              key={theme._id}
                              className={cn(
                                'cursor-pointer transition-all duration-300 rounded-lg overflow-hidden group border',
                                isSelected
                                  ? 'ring-2 ring-[#ff7865] shadow-md bg-gradient-to-br from-orange-100 via-red-100 to-pink-100 border-[#ff7865]'
                                  : 'border-gray-200 hover:border-[#ff7865]/50 hover:shadow-sm'
                              )}
                              onClick={() => handleThemeFocus(theme)}
                            >
                              <CardContent className="p-2 flex flex-col items-center justify-center h-16 relative">
                                {isSelected && (
                                  <div className="absolute top-1 right-1 w-3 h-3 bg-[#ff7865] rounded-full flex items-center justify-center">
                                    <CheckCircle2 className="w-2 h-2 text-white" />
                                  </div>
                                )}
                                <div className="w-6 h-6 mb-1 flex items-center justify-center">
                                  {theme.image ? (
                                    <img
                                      src={`https://tripemilestone.in-maa-1.linodeobjects.com/${theme.image}`}
                                      alt={theme.interestName}
                                      className="w-full h-full object-contain rounded-full"
                                    />
                                  ) : (
                                    <Sparkles className="w-4 h-4 text-gray-400" />
                                  )}
                                </div>
                                <span className="text-xs font-medium text-center text-gray-700">
                                  {theme.interestName}
                                </span>
                              </CardContent>
                            </Card>
                          );
                        })}
                      </div>
                    )}
                  </div>

                  {/* Adults */}
                  <div className="flex items-center justify-between">
                    <div>
                      <div className="font-medium">Adults</div>
                      <div className="text-sm text-gray-500">Ages 13 or above</div>
                    </div>
                    <div className="flex items-center gap-3">
                      <Button
                        variant="outline"
                        size="sm"
                        className="w-8 h-8 rounded-full p-0 bg-transparent hover:bg-red-50 hover:text-red-600 transition-colors"
                        onClick={() => handleAdultsChange(Math.max(1, adults - 1))}
                        disabled={adults <= 1}
                      >
                        -
                      </Button>
                      <span className="w-8 text-center font-medium">{adults}</span>
                      <Button
                        variant="outline"
                        size="sm"
                        className="w-8 h-8 rounded-full p-0 bg-transparent hover:bg-green-50 hover:text-green-600 transition-colors"
                        onClick={() => handleAdultsChange(adults + 1)}
                      >
                        +
                      </Button>
                    </div>
                  </div>

                  {/* Children */}
                  {showChild && (
                    <div className="flex items-center justify-between">
                      <div>
                        <div className="font-medium">Children</div>
                        <div className="text-sm text-gray-500">Ages 5-11</div>
                      </div>
                      <div className="flex items-center gap-3">
                        <Button
                          variant="outline"
                          size="sm"
                          className="w-8 h-8 rounded-full p-0 bg-transparent hover:bg-red-50 hover:text-red-600 transition-colors"
                          onClick={() => handleChildrenChange(Math.max(0, children - 1))}
                          disabled={children <= 0}
                        >
                          -
                        </Button>
                        <span className="w-8 text-center font-medium">{children}</span>
                        <Button
                          variant="outline"
                          size="sm"
                          className="w-8 h-8 rounded-full p-0 bg-transparent hover:bg-green-50 hover:text-green-600 transition-colors"
                          onClick={() => handleChildrenChange(children + 1)}
                        >
                          +
                        </Button>
                      </div>
                    </div>
                  )}

                  {/* Rooms */}
                  <div className="flex items-center justify-between">
                    <div>
                      <div className="font-medium">Rooms</div>
                      <div className="text-sm text-gray-500">Based on guests</div>
                    </div>
                    <div className="flex items-center gap-3">
                      <Button
                        variant="outline"
                        size="sm"
                        className="w-8 h-8 rounded-full p-0 bg-transparent hover:bg-red-50 hover:text-red-600 transition-colors"
                        onClick={() => setRooms(Math.max(minRooms, rooms - 1))}
                        disabled={rooms <= minRooms}
                      >
                        -
                      </Button>
                      <span className="w-8 text-center font-medium">{rooms}</span>
                      <Button
                        variant="outline"
                        size="sm"
                        className="w-8 h-8 rounded-full p-0 bg-transparent hover:bg-green-50 hover:text-green-600 transition-colors"
                        onClick={() => setRooms(Math.min(adults, rooms + 1))}
                        disabled={rooms >= adults}
                      >
                        +
                      </Button>
                    </div>
                  </div>
                </div>
              </PopoverContent>
            </Popover>

            {/* Search Button */}
            <Button
              className="ml-2 bg-app-secondary hover:bg-app-secondary text-white rounded-full w-12 h-12 p-0 flex items-center justify-center z-[110]"
              onClick={handleSearch}
            >
              <Search className="w-4 h-4" />
            </Button>
          </div>
        </div>
      </div>

      {/* Enhanced floating elements for a more dynamic feel */}
      <div className="absolute top-20 left-10 w-28 h-28 bg-emerald-400/20 backdrop-blur-sm rounded-full animate-pulse-slow hidden lg:block"></div>
      <div className="absolute bottom-32 right-16 w-24 h-24 bg-blue-400/20 backdrop-blur-sm rounded-full animate-pulse-slow delay-700 hidden lg:block"></div>
      <div className="absolute top-1/3 right-24 w-20 h-20 bg-purple-400/20 backdrop-blur-sm rounded-full animate-pulse-slow delay-300 hidden lg:block"></div>
      <div className="absolute bottom-1/4 left-24 w-16 h-16 bg-yellow-400/20 backdrop-blur-sm rounded-full animate-pulse-slow delay-1000 hidden lg:block"></div>
    </section>
  );
};

export default SearchPackage;
